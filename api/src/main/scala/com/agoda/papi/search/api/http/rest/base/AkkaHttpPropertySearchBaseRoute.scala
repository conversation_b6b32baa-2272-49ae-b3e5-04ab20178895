package com.agoda.papi.search.api.http.rest.base

import akka.http.scaladsl.server.{ Directive0, Route }
import com.agoda.commons.http.server.jackson.Implicits._
import com.agoda.commons.http.server.models.RequestContext
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.core.search.models.enumeration.SearchType
import com.agoda.papi.search.api.util.directive.{ AkkaHttpReadHeaderDirective, AkkaHttpRequestValidationDirective }
import com.agoda.papi.search.api.util.helper.AkkaHttpInternalServerErrorHandlerHelper.{
  internalServerErrorHandler,
  logRestEndpointERROR
}
import com.agoda.papi.search.api.util.helper.AkkaHttpOnCompleteHelper.onCompleteRequest
import com.agoda.papi.search.common.service.RocketmilesRankingExperimentService
import com.agoda.papi.search.common.util.MseHelper
import com.agoda.papi.search.common.util.RetryRequestChecker
import com.agoda.papi.search.common.util.measurement.MeasurementTag
import com.agoda.papi.search.common.util.circuitbreaker.EndpointCircuitBreakerWrapper
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.common.util.measurement.APIInstrumentedMetric
import com.agoda.papi.search.property.externaldependency.logger.HadoopLogger
import org.joda.time.DateTime
import request.{ BasePropertyRequest, PropertyRequest, SearchTypes }
import com.agoda.papi.search.property.service.{
  PreAllocationExperimentReportHelper,
  PropertySearchTokenService,
  StarfruitService
}
import com.agoda.platform.service.context.akka.GlobalContextDirectives.withGlobalContext
import io.opentelemetry.api.common.AttributeKey

import scala.concurrent.{ ExecutionContext, Future }
import scala.reflect.ClassTag

trait AkkaHttpPropertySearchBaseRoute
    extends AkkaHttpBaseRoute with AkkaHttpReadHeaderDirective with AkkaHttpRequestValidationDirective
      with APIInstrumentedMetric {

  implicit val ec: ExecutionContext
  val hadoopLogger: HadoopLogger
  val preAllocationExperimentReportHelper: PreAllocationExperimentReportHelper
  val starfruitService: StarfruitService
  val handlePiiHeaders: Directive0
  implicit val reportingService: MetricsReporter
  val propertySearchTokenService: PropertySearchTokenService
  val rocketmilesRankingExperimentService: RocketmilesRankingExperimentService

  protected def setupPropertySearchRouteWithExperiment[
      IN <: PropertyRequest: ClassTag,
      OUT <: Serializable with AnyRef
  ](
      serviceName: String,
      servicePath: String,
      searchType: SearchType,
      searchEndpoint: request.SearchType,
      breaker: EndpointCircuitBreakerWrapper
  )(response: (IN, ContextHolder) => Future[OUT])(implicit requestContext: RequestContext): Route =
    post {
      isRetryHeader { isRetryHeaderValue =>
        handlePiiHeaders {
          getAuthorizationHeader { authToken =>
            getPartnerClaimHeader { partnerClaimTokenFromHeader =>
              geoLocationHeader { encryptedGeolocation =>
                withGlobalContext { globalContext =>
                  pathEnd(
                    withRequest[IN](toDeserializer[IN], requestContext) { originalRequest =>
                      val requestWithOverriddenBookingDate = if (searchEndpoint == SearchTypes.Booking) {
                        originalRequest
                          .copy(
                            pricing = originalRequest.pricing.map(
                              _.copy(
                                bookingDate = DateTime.parse("2020-07-16T11:43:47.936+07:00")
                              )
                            )
                          )
                          .asInstanceOf[IN]
                      } else {
                        originalRequest
                      }

                      implicit val metricsContext = requestContext.metrics
                      metricsContext.setTag(MeasurementTag.funnel, requestWithOverriddenBookingDate.getFunnel.i)
                      metricsContext.setTag(
                        MeasurementTag.IsReBookingRequest,
                        requestWithOverriddenBookingDate.booking.flatMap(_.reBookingRequest).isDefined.toString
                      )
                      val propertySearchTokenOverriddenRequest =
                        propertySearchTokenService.overridePropertySearchRequest(requestWithOverriddenBookingDate)
                      onCompleteRequest(
                        propertySearchTokenOverriddenRequest,
                        requestWithOverriddenBookingDate,
                        servicePath,
                        hadoopLogger
                      ) { withOverriddenRequest =>
                        val asqOverridedRequest =
                          withOverriddenRequest.toOverrideFeatureFlagRequestForAsq().toRequestWithTrimmedPropertyIds()
                        val partnerClaimToken = partnerClaimTokenFromHeader.orElse(
                          asqOverridedRequest.pricing.flatMap(_.externalLoyalty.flatMap(_.partnerClaimToken))
                        )
                        val overrideRequest = asqOverridedRequest
                          .copy(
                            searchType = Option(searchEndpoint),
                            context = asqOverridedRequest.context.copy(
                              searchType = Some(searchType),
                              isRetry = Some(
                                RetryRequestChecker.isRetryRequest(
                                  globalContext,
                                  isRetryHeaderValue = Some(isRetryHeaderValue),
                                  isRetryFromRequestContext = asqOverridedRequest.context.isRetry
                                )
                              ),
                              encryptedGeolocation = encryptedGeolocation,
                              partnerClaimToken = partnerClaimToken,
                              capiToken = authToken
                            )
                          )
                          .asInstanceOf[IN]
                        activateTrace {
                          handleExceptions(internalServerErrorHandler(overrideRequest.context, requestContext)) {
                            validateRequest(overrideRequest, metricsContext, globalContext.getEnv) { validatedRequest =>
                              val msePricingSearchCriteria =
                                MseHelper.getMsePricingSearchCriteria(globalContext, validatedRequest)
                              val contextHolderF = createContextHolderF[IN](
                                validatedRequest,
                                validatedRequest.context,
                                searchType,
                                servicePath,
                                validatedRequest.pricing.isDefined,
                                globalContext,
                                msePricingSearchCriteria
                              )
                              val resultF = contextHolderF.flatMap { contextHolder =>
                                val isRocketMile =
                                  rocketmilesRankingExperimentService.isRankingExperimentEnabled(contextHolder)
                                requestContext.metrics.setAttribute(
                                  AttributeKey.stringKey(MeasurementTag.isRocketMile),
                                  isRocketMile.toString
                                )
                                papiExperimentManager.completeExperiment(
                                  breaker.execute(response(validatedRequest, contextHolder))
                                )(contextHolder.experimentContext, ec)
                              }
                              logRestEndpointERROR(resultF, overrideRequest.context, requestContext)
                              onCompleteRequest(resultF, validatedRequest, servicePath, hadoopLogger) { success =>
                                complete(success)
                              }
                            }
                          }
                        }
                      }
                    }
                  )
                }
              }
            }
          }
        }
      }
    }

  protected def setupBasePropertyRequestRouteWithExperiment[
      IN <: BasePropertyRequest: ClassTag,
      OUT <: Serializable with AnyRef
  ](
      serviceName: String,
      servicePath: String,
      searchType: SearchType,
      searchEndpoint: request.SearchType,
      breaker: EndpointCircuitBreakerWrapper
  )(response: (IN, ContextHolder) => Future[OUT])(implicit requestContext: RequestContext): Route =
    post {
      isRetryHeader { isRetryHeaderValue =>
        withGlobalContext { globalContext =>
          pathEnd(
            withRequest[IN](toDeserializer[IN], requestContext) { originalRequest =>
              implicit val metricsContext = requestContext.metrics
              val overridedRequest = originalRequest
                .withContext(
                  context = originalRequest.context.copy(
                    searchType = Some(searchType),
                    isRetry = Some(
                      RetryRequestChecker.isRetryRequest(
                        globalContext,
                        isRetryHeaderValue = Some(isRetryHeaderValue),
                        isRetryFromRequestContext = originalRequest.context.isRetry
                      )
                    )
                  )
                )
                .asInstanceOf[IN]

              handleExceptions(internalServerErrorHandler(overridedRequest.context, requestContext)) {
                validateRequest(overridedRequest, metricsContext, globalContext.getEnv) { validatedRequest =>
                  val msePricingSearchCriteria = MseHelper.getMsePricingSearchCriteria(globalContext, validatedRequest)
                  val contextHolderF = createContextHolderF[IN](
                    validatedRequest,
                    validatedRequest.context,
                    searchType,
                    servicePath,
                    validatedRequest.pricing.isDefined,
                    globalContext,
                    msePricingSearchCriteria
                  )
                  val resultF = contextHolderF.flatMap { contextHolder =>
                    val isRocketMile = rocketmilesRankingExperimentService.isRankingExperimentEnabled(contextHolder)
                    requestContext.metrics.setAttribute(
                      AttributeKey.stringKey(MeasurementTag.isRocketMile),
                      isRocketMile.toString
                    )
                    papiExperimentManager.completeExperiment(
                      breaker.execute(response(validatedRequest, contextHolder))
                    )(contextHolder.experimentContext, ec)
                  }
                  logRestEndpointERROR(resultF, overridedRequest.context, requestContext)
                  onCompleteRequest(resultF, validatedRequest, servicePath, hadoopLogger) { success =>
                    complete(success)
                  }
                }
              }
            }
          )
        }
      }
    }

}
