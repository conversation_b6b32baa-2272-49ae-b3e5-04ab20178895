package com.agoda.papi.search.api.http.rest

import akka.http.scaladsl.server.Route
import com.agoda.commons.http.server.akka.directives.ServiceDirectives
import com.agoda.commons.http.server.models.RequestContext
import com.agoda.core.search.models.enumeration
import com.agoda.papi.search.api.http.rest.base.AkkaHttpPropertySearchBaseRoute
import com.agoda.papi.search.api.http.rest.openapi.endpointdocs.BookingDoc
import com.agoda.papi.search.common.util.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, RetryRequestChecker }
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementTag }
import com.agoda.papi.search.property.api.metrics.PollingMetricsHelper
import com.agoda.platform.service.context.akka.GlobalContextDirectives.withGlobalContext
import com.agoda.papi.search.common.util.PackageMetricHelper
import org.joda.time.DateTime
import request.{ PropertyRequest, SearchTypes }
import sttp.tapir.server.akkahttp.AkkaHttpServerInterpreter
import transformers.Properties

import scala.concurrent.Future

trait AkkaHttpBookingRoute extends AkkaHttpPropertySearchBaseRoute with ServiceDirectives {

  private val serviceName: String   = "booking-endpoint"
  private val servicePath: String   = "/api/booking"
  private val servicePathV2: String = "/api/v2/booking"

  val bookingDoc: BookingDoc

  def bookingRoutesV2(implicit requestContext: RequestContext): Route =
    isRetryHeader { isRetryHeaderValue =>
      handlePiiHeaders {
        getAuthorizationHeader { authToken =>
          getPartnerClaimHeader { partnerClaimTokenFromHeader =>
            geoLocationHeader { encryptedGeolocation =>
              withGlobalContext { globalContext =>
                activateTrace {
                  AkkaHttpServerInterpreter().toRoute {
                    bookingDoc.bookingEndpointDoc.serverLogic[Future] { input =>
                      val originalRequest = input._1

                      val requestWithOverriddenBookingDate = originalRequest.copy(
                        pricing = originalRequest.pricing.map(
                          _.copy(
                            bookingDate = DateTime.parse("2020-07-16T11:43:47.936+07:00")
                          )
                        )
                      )

                      val searchType              = enumeration.SearchTypes.BookingSearch
                      implicit val metricsContext = requestContext.metrics
                      metricsContext.setTag(MeasurementTag.funnel, requestWithOverriddenBookingDate.getFunnel.i)
                      metricsContext.setTag(
                        MeasurementTag.IsReBookingRequest,
                        requestWithOverriddenBookingDate.booking.flatMap(_.reBookingRequest).isDefined.toString
                      )

                      val propertySearchTokenOverriddenRequest =
                        propertySearchTokenService.overridePropertySearchRequest(requestWithOverriddenBookingDate)

                      propertySearchTokenOverriddenRequest.flatMap { withOverriddenRequest =>
                        val asqOverridedRequest =
                          withOverriddenRequest.toOverrideFeatureFlagRequestForAsq().toRequestWithTrimmedPropertyIds()
                        val partnerClaimToken = partnerClaimTokenFromHeader.orElse(
                          asqOverridedRequest.pricing.flatMap(_.externalLoyalty.flatMap(_.partnerClaimToken))
                        )
                        val overrideRequest = asqOverridedRequest
                          .copy(
                            searchType = Option(SearchTypes.Booking),
                            context = asqOverridedRequest.context.copy(
                              searchType = Some(searchType),
                              isRetry = Some(
                                RetryRequestChecker.isRetryRequest(
                                  globalContext,
                                  isRetryHeaderValue = Some(isRetryHeaderValue),
                                  isRetryFromRequestContext = asqOverridedRequest.context.isRetry
                                )
                              ),
                              encryptedGeolocation = encryptedGeolocation,
                              partnerClaimToken = partnerClaimToken,
                              capiToken = authToken
                            )
                          )

                        val msePricingSearchCriteria =
                          MseHelper.getMsePricingSearchCriteria(globalContext, overrideRequest)
                        val contextHolderF = createContextHolderF[PropertyRequest](
                          overrideRequest,
                          overrideRequest.context,
                          searchType,
                          servicePathV2,
                          overrideRequest.pricing.isDefined,
                          globalContext,
                          msePricingSearchCriteria
                        )

                        val resultF = contextHolderF.flatMap { contextHolder =>
                          papiExperimentManager.completeExperiment(
                            circuitBreakerRegistry.booking.execute(
                              starfruitService.doSearchEnriched(overrideRequest, contextHolder)
                            )
                          )(contextHolder.experimentContext, ec)
                        }

                        val response = for {
                          contextHolder <- contextHolderF
                          response      <- resultF
                        } yield {
                          hadoopLogger.composeAndSendPAPISearchMessage(
                            overrideRequest,
                            response,
                            SearchTypes.Booking.i,
                            whiteLabelService.getWhiteLabelIdFromToken(
                              overrideRequest.context.whiteLabelKey,
                              contextHolder.globalContext.getEnv
                            )
                          )(contextHolder)
                          hadoopLogger.composeAndSendSearchPollPricing(
                            overrideRequest,
                            response,
                            SearchTypes.Booking.i
                          )
                          reportingService.report(
                            MeasurementKey.HotelPerRequest,
                            overrideRequest.context.propertyIds.length,
                            Map.empty
                          )
                          reportingService.report(
                            MeasurementKey.RequestPollingInfo,
                            1L,
                            PollingMetricsHelper.getPollingMetricTags(overrideRequest, SearchTypes.Booking.i)
                          )
                          overrideRequest.pricing.foreach { pricing =>
                            val yesterday = pricing.bookingDate.withTimeAtStartOfDay().minusDays(1)
                            if (pricing.checkIn.isBefore(yesterday)) {
                              reportingService.report(
                                MeasurementKey.PastCheckIn,
                                1L,
                                Map(
                                  MeasurementTag.funnel     -> overrideRequest.getFunnel.toString,
                                  MeasurementTag.SearchType -> SearchTypes.Booking.i
                                )
                              )
                            }
                          }
                          preAllocationExperimentReportHelper.reportPreAllocatedExperiment(response, overrideRequest)(
                            contextHolder,
                            ec
                          )
                          // TODO: Remove this once Package migrated to Cart in all platforms (copied from booking v1)
                          if (overrideRequest.isPackagingSearch) {
                            reportingService.report(
                              MeasurementKey.PackageNumberOfRequest,
                              1L,
                              PackageMetricHelper.getPackagingTags(overrideRequest, servicePathV2)
                            )
                          }
                          response
                        }

                        response.transformWith {
                          case scala.util.Success(value) => Future.successful(Right(value))
                          case scala.util.Failure(exception) =>
                            Future.successful(Left(exception.getMessage))
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

  def bookingRoutes(implicit requestContext: RequestContext): Route = pathPrefix("api" / "booking") {
    val search = enumeration.SearchTypes.BookingSearch
    setupPropertySearchRouteWithExperiment[PropertyRequest, Properties](
      serviceName,
      servicePath,
      search,
      SearchTypes.Booking,
      circuitBreakerRegistry.booking
    ) { (request, contextHolder) =>
      for {
        response <- starfruitService.doSearchEnriched(request, contextHolder)
      } yield {
        preAllocationExperimentReportHelper.reportPreAllocatedExperiment(response, request)(contextHolder, ec)
        reportingService.report(MeasurementKey.HotelPerRequest, request.context.propertyIds.length, Map.empty)
        // TODO: Remove this once Package migrated to Cart in all platforms
        if (request.isPackagingSearch) {
          reportingService.report(
            MeasurementKey.PackageNumberOfRequest,
            1L,
            PackageMetricHelper.getPackagingTags(request, servicePath)
          )
        }
        response
      }
    }
  }

}
